# Research 节点重复开始事件修复方案

## 问题描述

在使用 LangGraph 工作流时，发现 `research` 节点的开始状态被输出了两次。该节点中包含了 `create_react_agent` 功能。

## 问题原因分析

### 根本原因
1. **LangGraph 事件层级问题**：`create_react_agent` 创建的 agent 本身也是一个 LangGraph 图
2. **子图事件泄露**：当 `create_react_agent` 在 `research_node` 中被调用时，会产生自己的 `on_chain_start` 事件
3. **事件过滤不够精确**：原始代码只检查了 `event_type` 和节点名称，没有区分顶层节点事件和子图事件

### 事件来源
- **第一个 "started" 事件**：来自 research 节点本身的开始
- **第二个 "started" 事件**：来自 `create_react_agent` 内部图的开始

## 解决方案

### 1. 多层过滤策略

在 `workflow.py` 中实现了三层过滤机制：

```python
# 方法1: 检查父级运行ID
parent_ids = event.get('parent_ids', [])
if parent_ids:
    logger.debug(f"过滤掉子图事件: {node_name}, parent_ids: {parent_ids}")
    continue

# 方法2: 检查事件的运行名称是否包含子图标识
run_name = event.get('data', {}).get('input', {}).get('run_name', '')
if 'react' in str(run_name).lower() or 'agent' in str(run_name).lower():
    logger.debug(f"过滤掉 react agent 子图事件: {node_name}, run_name: {run_name}")
    continue

# 方法3: 检查标签
tags = event.get('tags', [])
if any('react' in str(tag).lower() or 'agent' in str(tag).lower() for tag in tags):
    logger.debug(f"过滤掉带有 react/agent 标签的事件: {node_name}, tags: {tags}")
    continue
```

### 2. 事件计数器

添加了事件计数器来监控和记录重复事件：

```python
# 添加事件计数器来跟踪重复事件
node_start_counts = {node: 0 for node in valid_nodes}

# 在处理开始事件时
node_start_counts[node_name] += 1
if node_start_counts[node_name] > 1:
    logger.warning(f"检测到重复的开始事件: {node_name} (第{node_start_counts[node_name]}次)")
```

### 3. 一致性处理

对 `on_chain_start` 和 `on_chain_end` 事件应用相同的过滤逻辑，确保开始和结束事件的一致性。

## 修改的文件

### `agent-runner/app/agents/workflow.py`

- **第 103-107 行**：添加事件计数器
- **第 119-140 行**：实现多层过滤策略（开始事件）
- **第 142-148 行**：添加计数器和警告日志
- **第 195-207 行**：应用相同过滤逻辑（结束事件）

## 测试和验证

### 测试脚本

创建了两个测试脚本：

1. **`test_duplicate_events.py`**：完整的工作流测试，统计各节点的事件数量
2. **`debug_events.py`**：原始事件调试脚本，查看 LangGraph 事件结构

### 验证方法

```bash
# 运行测试脚本
cd agent-runner
python test_duplicate_events.py

# 查看调试信息
python debug_events.py
```

### 预期结果

- ✅ Research 节点只有 1 个开始事件
- ✅ Research 节点只有 1 个结束事件
- ✅ 流式处理事件正常
- ✅ 其他节点事件不受影响

## 技术细节

### LangGraph 事件结构

LangGraph 的 `astream_events` 会产生以下类型的事件：
- `on_chain_start`：链/节点开始执行
- `on_chain_stream`：链/节点流式输出
- `on_chain_end`：链/节点执行结束

### 子图识别方法

1. **parent_ids**：子图事件会有父级运行ID
2. **run_name**：包含 'react' 或 'agent' 的运行名称
3. **tags**：包含特定标签的事件

## 注意事项

1. **调试日志**：修复后会产生调试日志，可以通过设置日志级别来控制
2. **性能影响**：过滤逻辑对性能影响很小
3. **兼容性**：修复方案与现有代码完全兼容
4. **扩展性**：过滤策略可以轻松扩展到其他类型的子图

## 后续优化建议

1. **配置化过滤规则**：将过滤规则配置化，便于维护
2. **事件监控**：添加更详细的事件监控和统计
3. **单元测试**：为事件过滤逻辑添加单元测试
4. **文档更新**：更新相关文档说明事件处理机制
