"""LangGraph agent workflow implementation."""

import asyncio
import logging
from typing import As<PERSON><PERSON><PERSON>ator, Dict, Any, List, Optional
from datetime import datetime, timezone
import uuid

from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel, Field, SecretStr

from app.config.settings import settings
from app.models.schemas import AgentStatus, SSEMessage, StreamData
from app.agents.types import AgentState, create_agent_state
from .nodes import comprehensive_retrieval_node, human_feedback_node, planning_node, report_node, research_node
from langgraph.types import Command


logger = logging.getLogger(__name__)


class AgentWorkflow:
    """Main agent workflow using LangGraph."""
    
    def __init__(self):
        """Initialize the agent workflow."""
        api_key = SecretStr(settings.openai_api_key) if settings.openai_api_key else None
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            api_key=api_key,
            base_url=settings.openai_base_url,
            temperature=0.7,
            streaming=True
        )
        # Initialize memory saver for thread-based persistence
        self.memory_saver = MemorySaver()
        self.graph = self._build_graph()
    
    def _build_graph(self):
        """Build the LangGraph workflow."""
        
        # Define the workflow
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("retrieval", comprehensive_retrieval_node)
        workflow.add_node("planning", planning_node)
        workflow.add_node("human_feedback", human_feedback_node)
        workflow.add_node("research", research_node)
        workflow.add_node("report", report_node)
        
        # Add edges
        workflow.add_edge("planning", "human_feedback")
        workflow.add_edge("research", "report")
        workflow.add_edge("report", END)
       
        
        # Set entry point
        workflow.set_entry_point("retrieval")
        
        # Compile with memory saver for thread-based persistence
        return workflow.compile(checkpointer=self.memory_saver)
    

    async def run(
        self,
        query: str,
        session_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        interrupt_feedback: Optional[Dict] = None,
        case_id: Optional[str] = None
    ) -> AsyncGenerator[StreamData, None]:
        """Run the agent workflow with streaming."""
        
        # Create initial state
        state = create_agent_state(query=query, session_id=session_id, metadata=metadata,case_id=case_id)
        if session_id:
            state['session_id'] = session_id
        if metadata:
            state['metadata'] = metadata
        
        # Create config for thread_id support
        config: RunnableConfig = {"configurable": {}}
        if thread_id is None:
            thread_id = str(uuid.uuid4())

        config["configurable"]["thread_id"] = thread_id 
        # 中断恢复输入
        if interrupt_feedback:
            state = Command(resume=interrupt_feedback)
        
        try:
            # 定义我们想要监控的节点列表
            valid_nodes = ["retrieval", "planning", "human_feedback", "research", "report"]

            # Use astream_events with config for thread_id support to get more detailed events
            async for event in self.graph.astream_events(state, config=config, version="v2"):
                # 获取事件类型和数据
                event_type = event.get("event", "")
                data = event.get("data", {})
                metadata = data.get("metadata", {})
                
                # 只处理我们定义的节点
                node_name = event.get('name', '')
                
                if node_name not in valid_nodes:
                    continue           
     
                # 检查事件来源 - 只处理顶层节点事件
                if event_type == "on_chain_start":

     
                    yield StreamData(
                        node=node_name,
                        status="started",
                        thread_id=thread_id,
                        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        error=None
                    )
                
                # 处理节点流式输出事件
                elif event_type == "on_chain_stream":
                    chunk = data.get("chunk", {})
                    
                    # 处理 research 节点的流式输出
                    if node_name == "research":
                        # 检查是否是我们想要输出的结构
                        if (isinstance(chunk, dict) and
                            "data" in chunk and
                            isinstance(chunk["data"], dict) and
                            "demand_info" in chunk["data"] and
                            "final_result" in chunk["data"]):
                            # 输出 research 流式结果，将原始数据包装为 StreamData 对象
                            research_data = chunk["data"]
                            # 创建 StreamData 对象，包含所有原始数据
                            stream_data = StreamData(
                                node=node_name,
                                status="processing",
                                thread_id=thread_id,
                                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                error=None
                            )
                            # 将原始数据中的所有字段添加到 StreamData 对象中
                            for key, value in research_data.items():
                                setattr(stream_data, key, value)
                            
                            yield stream_data
                    
                    # 处理 report 节点的流式输出
                    elif node_name == "report" and isinstance(chunk, dict):
                        # 输出报告流式结果，将原始数据包装为 StreamData 对象
                        # 创建 StreamData 对象，包含所有原始数据
                        stream_data = StreamData(
                            node=node_name,
                            status="processing",
                            thread_id=thread_id,
                            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            error=None
                        )
                        # 将原始数据中的所有字段添加到 StreamData 对象中
                        for key, value in chunk.items():
                            setattr(stream_data, key, value)
                        
                        yield stream_data
                
                # 处理节点结束事件
                elif event_type == "on_chain_end":                 
                    stream_data = StreamData(
                        node=node_name,
                        status="completed",
                        thread_id=thread_id,
                        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        error=None
                    )   
                    yield stream_data
                        
        except Exception as e:
            logger.error(f"Error in workflow execution: {str(e)}")
            yield StreamData(
                node="error",
                status="error",
                thread_id=thread_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                error=str(e)
            )
        

# Global agent instance
agent_workflow = AgentWorkflow()

if __name__ == "__main__":
    data = {
        "query": "张三密接人有哪些",
        "session_id": "123455",
        "metadata": {},
        "thread_id" : "cc7e47e4-1232-464c-81e2-5e8ac3db27dc",
        "interrupt_feedback": None,
        "case_id" : "123456"
        }
    
    # 运行工作流并打印 yield 的对象
    async def main():
        print("开始运行工作流...")
        async for message in agent_workflow.run(**data):
            print(f"收到消息: {message}")
            print(f"数据内容: {message.model_dump()}")
            print("-" * 50)
    
    # 运行异步主函数
    asyncio.run(main())
        
