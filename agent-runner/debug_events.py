#!/usr/bin/env python3
"""调试脚本：查看原始 LangGraph 事件结构"""

import asyncio
import logging
import json
from app.agents.workflow import agent_workflow

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_raw_events():
    """调试原始事件结构，特别关注 research 节点的事件"""
    
    test_data = {
        "query": "张三密接人有哪些",
        "session_id": "debug_session",
        "metadata": {},
        "thread_id": "debug_thread",
        "interrupt_feedback": None,
        "case_id": "debug_case"
    }
    
    print("开始调试原始事件结构...")
    print("=" * 80)
    
    # 直接访问 graph 的 astream_events
    from app.agents.types import create_agent_state
    from langchain_core.runnables import RunnableConfig
    import uuid
    
    # 创建状态和配置
    state = create_agent_state(**test_data)
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    research_events = []
    event_count = 0
    
    try:
        async for event in agent_workflow.graph.astream_events(state, config=config, version="v2"):
            event_count += 1
            
            # 获取基本信息
            event_type = event.get("event", "")
            node_name = event.get('name', '')
            
            # 只关注 research 节点的事件
            if node_name == "research":
                research_events.append(event)
                
                print(f"\n🔍 Research 事件 #{len(research_events)}:")
                print(f"  事件类型: {event_type}")
                print(f"  节点名称: {node_name}")
                print(f"  运行ID: {event.get('run_id', 'N/A')}")
                print(f"  父级IDs: {event.get('parent_ids', [])}")
                print(f"  标签: {event.get('tags', [])}")
                
                # 检查元数据
                metadata = event.get('metadata', {})
                if metadata:
                    print(f"  元数据: {json.dumps(metadata, indent=4, ensure_ascii=False)}")
                
                # 检查数据
                data = event.get('data', {})
                if data:
                    print(f"  数据键: {list(data.keys())}")
                
                print("-" * 60)
            
            # 限制总事件数量，避免无限循环
            if event_count > 100:
                print("⚠️ 达到事件数量限制，停止调试")
                break
                
    except Exception as e:
        logger.error(f"调试过程中出现错误: {str(e)}")
        print(f"❌ 调试失败: {str(e)}")
        return
    
    # 分析结果
    print("\n" + "=" * 80)
    print("Research 节点事件分析:")
    print("=" * 80)
    
    start_events = [e for e in research_events if e.get('event') == 'on_chain_start']
    end_events = [e for e in research_events if e.get('event') == 'on_chain_end']
    stream_events = [e for e in research_events if e.get('event') == 'on_chain_stream']
    
    print(f"开始事件数量: {len(start_events)}")
    print(f"结束事件数量: {len(end_events)}")
    print(f"流式事件数量: {len(stream_events)}")
    
    if len(start_events) > 1:
        print("\n🚨 发现多个开始事件，详细分析:")
        for i, event in enumerate(start_events, 1):
            print(f"\n开始事件 #{i}:")
            print(f"  父级IDs: {event.get('parent_ids', [])}")
            print(f"  标签: {event.get('tags', [])}")
            print(f"  运行ID: {event.get('run_id', 'N/A')}")
    else:
        print("✅ 开始事件数量正常")

if __name__ == "__main__":
    asyncio.run(debug_raw_events())
