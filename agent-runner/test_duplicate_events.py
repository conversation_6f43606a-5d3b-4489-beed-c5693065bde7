#!/usr/bin/env python3
"""测试脚本：验证 research 节点重复事件修复效果"""

import asyncio
import logging
from app.agents.workflow import agent_workflow

# 设置日志级别为 DEBUG 以查看过滤日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_research_node_events():
    """测试 research 节点是否还会产生重复的开始事件"""
    
    test_data = {
        "query": "张三密接人有哪些",
        "session_id": "test_session_123",
        "metadata": {},
        "thread_id": "test_thread_456",
        "interrupt_feedback": None,
        "case_id": "test_case_789"
    }
    
    print("开始测试 research 节点重复事件修复...")
    print("=" * 60)
    
    # 统计各个节点的开始和结束事件
    event_counts = {}
    
    try:
        async for stream_data in agent_workflow.run(**test_data):
            node = stream_data.node
            status = stream_data.status
            
            # 初始化节点计数器
            if node not in event_counts:
                event_counts[node] = {"started": 0, "completed": 0, "processing": 0}
            
            # 统计事件
            if status in event_counts[node]:
                event_counts[node][status] += 1
            
            # 打印事件信息
            print(f"节点: {node:12} | 状态: {status:10} | 时间: {stream_data.timestamp}")
            
            # 特别关注 research 节点的开始事件
            if node == "research" and status == "started":
                print(f"  ⚠️  Research 节点开始事件 #{event_counts[node]['started']}")
            
            # 如果有额外的数据字段，也打印出来
            if hasattr(stream_data, 'demand_info'):
                print(f"    需求信息: {stream_data.demand_info}")
            if hasattr(stream_data, 'final_result'):
                print(f"    最终结果: {stream_data.final_result[:50]}..." if len(str(stream_data.final_result)) > 50 else f"    最终结果: {stream_data.final_result}")
            
            print("-" * 60)
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
        return
    
    # 分析结果
    print("\n" + "=" * 60)
    print("事件统计结果:")
    print("=" * 60)
    
    for node, counts in event_counts.items():
        print(f"节点 {node}:")
        for status, count in counts.items():
            if count > 0:
                print(f"  {status}: {count} 次")
        print()
    
    # 检查 research 节点是否有重复的开始事件
    research_started = event_counts.get("research", {}).get("started", 0)
    if research_started > 1:
        print(f"❌ 发现问题: research 节点有 {research_started} 个开始事件（应该只有1个）")
    elif research_started == 1:
        print("✅ 修复成功: research 节点只有1个开始事件")
    else:
        print("⚠️  警告: research 节点没有开始事件")

if __name__ == "__main__":
    asyncio.run(test_research_node_events())
